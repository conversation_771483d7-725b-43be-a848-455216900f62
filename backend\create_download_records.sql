-- 创建测试下载记录
-- 首先获取用户fjj的ID
SET @user_id = (SELECT id FROM users WHERE username = 'fjj' LIMIT 1);

-- 清除现有的下载记录
DELETE FROM download_records WHERE user_id = @user_id;

-- 插入测试下载记录
INSERT INTO download_records 
(file_id, user_id, download_type, zip_filename, zip_path, file_size, 
 is_encrypted, password, download_status, created_at, downloaded_at)
VALUES 
(1, @user_id, 'single', 'test_image_1_20251211_210000.zip', '/tmp/downloads/test_image_1_20251211_210000.zip', 1048576, 1, 'pass1', 'completed', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(2, @user_id, 'single', 'test_image_2_20251211_200000.zip', '/tmp/downloads/test_image_2_20251211_200000.zip', 2097152, 0, NULL, 'completed', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(3, @user_id, 'batch', 'test_batch_20251211_190000.zip', '/tmp/downloads/test_batch_20251211_190000.zip', 3145728, 1, 'pass3', 'completed', NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY),
(4, @user_id, 'single', 'test_document_20251211_180000.zip', '/tmp/downloads/test_document_20251211_180000.zip', 4194304, 0, NULL, 'completed', NOW() - INTERVAL 4 DAY, NOW() - INTERVAL 4 DAY),
(5, @user_id, 'folder', 'test_folder_20251211_170000.zip', '/tmp/downloads/test_folder_20251211_170000.zip', 5242880, 1, 'pass5', 'completed', NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 5 DAY);

-- 显示插入的记录
SELECT 
    id, 
    file_id, 
    zip_filename, 
    download_type, 
    is_encrypted, 
    ROUND(file_size / 1024 / 1024, 2) as size_mb,
    created_at,
    downloaded_at
FROM download_records 
WHERE user_id = @user_id 
ORDER BY created_at DESC;
