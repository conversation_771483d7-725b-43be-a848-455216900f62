<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录功能测试 - 修复版</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/download-records.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        /* 下载记录视图样式 */
        .download-records-view {
            margin-top: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔽 下载记录功能测试 - 修复版</h1>
            <p>测试修复后的下载记录显示功能</p>
        </div>

        <div class="test-section">
            <h3>1. 登录测试</h3>
            <button onclick="testLogin()">使用fjj账户登录</button>
            <button onclick="checkToken()">检查当前Token</button>
            <div id="loginResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 下载记录视图测试</h3>
            <button onclick="testDownloadRecordsView()">测试下载记录视图</button>
            <button onclick="testWithRealData()">使用真实数据测试</button>
            <button onclick="clearView()">清空视图</button>
            <div id="downloadResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 下载记录视图预览</h3>
            <div id="downloadRecordsContainer">
                <!-- 下载记录视图将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/components.js"></script>
    <script src="js/api.js"></script>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let currentToken = localStorage.getItem('token');

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'fjj',
                        password: '123456'
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    currentToken = result.token;
                    localStorage.setItem('token', currentToken);
                    showResult('loginResult', `登录成功！\nToken: ${currentToken}`, 'success');
                } else {
                    showResult('loginResult', `登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录请求失败: ${error.message}`, 'error');
            }
        }

        function checkToken() {
            const token = localStorage.getItem('token');
            if (token) {
                showResult('loginResult', `当前Token: ${token}`, 'info');
            } else {
                showResult('loginResult', '没有找到Token，请先登录', 'error');
            }
        }

        function testDownloadRecordsView() {
            const container = document.getElementById('downloadRecordsContainer');
            
            // 创建下载记录视图HTML结构
            const html = `
                <div class="download-records-view" id="download-records-view">
                    <div class="download-records-header">
                        <h2>
                            <i class="fas fa-download"></i>
                            我的下载记录
                        </h2>
                        <div class="download-records-controls">
                            <button class="btn btn-primary" onclick="loadTestRecords()">
                                <i class="fas fa-sync-alt"></i>
                                加载测试数据
                            </button>
                        </div>
                    </div>
                    
                    <div class="download-records-content">
                        <!-- 加载状态 -->
                        <div class="loading-state" id="download-records-loading" style="display: none;">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <p>正在加载下载记录...</p>
                        </div>

                        <!-- 空状态 -->
                        <div class="empty-state" id="download-records-empty" style="display: block;">
                            <i class="fas fa-download"></i>
                            <h3>暂无下载记录</h3>
                            <p>您还没有下载过任何文件，点击上方按钮加载测试数据</p>
                        </div>

                        <!-- 下载记录列表 -->
                        <div class="download-records-list" id="download-records-list" style="display: none;">
                            <!-- 下载记录将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
            showResult('downloadResult', '下载记录视图已创建，点击"加载测试数据"按钮查看效果', 'success');
        }

        function loadTestRecords() {
            const loadingElement = document.getElementById('download-records-loading');
            const emptyElement = document.getElementById('download-records-empty');
            const listElement = document.getElementById('download-records-list');

            // 显示加载状态
            if (loadingElement) loadingElement.style.display = 'flex';
            if (emptyElement) emptyElement.style.display = 'none';
            if (listElement) listElement.style.display = 'none';

            // 模拟加载延迟
            setTimeout(() => {
                // 创建测试数据
                const testRecords = [
                    {
                        id: 1,
                        file_id: 1001,
                        filename: 'design_mockup.psd',
                        file_size: 15728640,
                        download_time: new Date().toISOString(),
                        download_type: 'single',
                        is_encrypted: true,
                        download_count: 1
                    },
                    {
                        id: 2,
                        file_id: 1002,
                        filename: 'project_photos.zip',
                        file_size: 52428800,
                        download_time: new Date(Date.now() - 3600000).toISOString(),
                        download_type: 'folder',
                        is_encrypted: false,
                        download_count: 3
                    },
                    {
                        id: 3,
                        file_id: 1003,
                        filename: 'logo_final.ai',
                        file_size: 2097152,
                        download_time: new Date(Date.now() - 7200000).toISOString(),
                        download_type: 'single',
                        is_encrypted: true,
                        download_count: 2
                    }
                ];

                // 渲染记录
                renderDownloadRecords(testRecords);

                // 隐藏加载状态，显示列表
                if (loadingElement) loadingElement.style.display = 'none';
                if (listElement) listElement.style.display = 'grid';

                showResult('downloadResult', `成功加载 ${testRecords.length} 条测试下载记录`, 'success');
            }, 1500);
        }

        function renderDownloadRecords(records) {
            const listElement = document.getElementById('download-records-list');
            if (!listElement) return;

            const recordsHtml = records.map(record => createDownloadRecordCard(record)).join('');
            listElement.innerHTML = recordsHtml;
        }

        function createDownloadRecordCard(record) {
            const fileIcon = getFileIcon(record.filename);
            const fileSize = formatFileSize(record.file_size || 0);
            const downloadTime = formatDateTime(record.download_time);
            const isEncrypted = record.is_encrypted;
            const downloadCount = record.download_count || 0;

            return `
                <div class="download-record-card" data-record-id="${record.id}">
                    <div class="record-header">
                        <div class="record-icon">
                            <i class="${fileIcon}"></i>
                            ${isEncrypted ? '<i class="fas fa-lock encrypted-badge"></i>' : ''}
                        </div>
                        <div class="record-info">
                            <h4 class="record-filename" title="${record.filename}">${record.filename}</h4>
                            <div class="record-meta">
                                <span><i class="fas fa-hdd"></i> ${fileSize}</span>
                                <span><i class="fas fa-clock"></i> ${downloadTime}</span>
                                <span><i class="fas fa-download"></i> ${downloadCount} 次</span>
                            </div>
                        </div>
                    </div>

                    ${isEncrypted ? `
                        <div class="record-stats">
                            <div class="encryption-status">
                                <i class="fas fa-lock"></i>
                                文件已加密
                            </div>
                        </div>
                    ` : ''}

                    <div class="record-actions">
                        <button class="btn btn-primary btn-sm" onclick="alert('重新下载: ${record.filename}')">
                            <i class="fas fa-download"></i>
                            重新下载
                        </button>
                        ${isEncrypted ? `
                            <button class="btn btn-secondary btn-sm" onclick="alert('获取密码: ${record.filename}')">
                                <i class="fas fa-key"></i>
                                获取密码
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function getFileIcon(filename) {
            if (!filename) return 'fas fa-file';
            const ext = filename.split('.').pop().toLowerCase();
            const iconMap = {
                'jpg': 'fas fa-image', 'jpeg': 'fas fa-image', 'png': 'fas fa-image',
                'psd': 'fas fa-image', 'ai': 'fas fa-image', 'eps': 'fas fa-image',
                'pdf': 'fas fa-file-pdf', 'zip': 'fas fa-file-archive',
                'mp4': 'fas fa-file-video', 'mp3': 'fas fa-file-audio'
            };
            return iconMap[ext] || 'fas fa-file';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            
            if (diffHours < 1) return '刚刚';
            if (diffHours < 24) return `${diffHours} 小时前`;
            if (diffHours < 48) return '昨天';
            return date.toLocaleDateString('zh-CN');
        }

        async function testWithRealData() {
            if (!currentToken) {
                showResult('downloadResult', '请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/records`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const result = await response.json();
                
                if (response.ok && result.records) {
                    // 确保视图已创建
                    if (!document.getElementById('download-records-view')) {
                        testDownloadRecordsView();
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    renderDownloadRecords(result.records);
                    
                    const listElement = document.getElementById('download-records-list');
                    const emptyElement = document.getElementById('download-records-empty');
                    
                    if (result.records.length > 0) {
                        if (listElement) listElement.style.display = 'grid';
                        if (emptyElement) emptyElement.style.display = 'none';
                        showResult('downloadResult', `成功加载 ${result.records.length} 条真实下载记录`, 'success');
                    } else {
                        if (listElement) listElement.style.display = 'none';
                        if (emptyElement) emptyElement.style.display = 'block';
                        showResult('downloadResult', '没有找到下载记录', 'info');
                    }
                } else {
                    showResult('downloadResult', `获取下载记录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('downloadResult', `请求失败: ${error.message}`, 'error');
            }
        }

        function clearView() {
            const container = document.getElementById('downloadRecordsContainer');
            container.innerHTML = '';
            showResult('downloadResult', '视图已清空', 'info');
        }

        // 页面加载时检查Token
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
