#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import os

def execute_sql_file():
    """执行SQL文件"""
    print("📝 执行SQL文件创建测试下载记录...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        # 读取SQL文件
        sql_file = os.path.join(os.path.dirname(__file__), 'create_download_records.sql')
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
        
        with connection.cursor() as cursor:
            for sql in sql_statements:
                if not sql:
                    continue
                    
                print(f"执行SQL: {sql[:50]}...")
                cursor.execute(sql)
                
                # 如果是SELECT语句，显示结果
                if sql.strip().upper().startswith('SELECT'):
                    results = cursor.fetchall()
                    if results:
                        print("查询结果:")
                        for row in results:
                            print(f"  {row}")
            
            connection.commit()
            print("✅ SQL文件执行成功！")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL文件执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    execute_sql_file()
