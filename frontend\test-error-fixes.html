<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-item h4 {
            margin-top: 0;
            color: #333;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 错误修复测试</h1>
            <p>测试并验证所有错误修复的效果</p>
        </div>

        <div class="test-item">
            <h4>🐛 修复的错误</h4>
            <div class="grid">
                <div>
                    <strong>1. CORS跨域错误</strong>
                    <ul>
                        <li>修复了API端口配置不匹配问题</li>
                        <li>将默认端口从8086改为8080</li>
                    </ul>
                </div>
                <div>
                    <strong>2. 认证头缺失</strong>
                    <ul>
                        <li>改进了token获取逻辑</li>
                        <li>增加了认证状态检查</li>
                    </ul>
                </div>
                <div>
                    <strong>3. 系统信息API错误</strong>
                    <ul>
                        <li>增加了详细的错误处理</li>
                        <li>避免认证错误影响应用启动</li>
                    </ul>
                </div>
                <div>
                    <strong>4. 下载记录API</strong>
                    <ul>
                        <li>修复了响应数据处理</li>
                        <li>增加了错误处理和重试机制</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-item">
            <h4>🧪 测试功能</h4>
            <div class="grid">
                <div>
                    <button class="btn" onclick="testAPIConnection()">测试API连接</button>
                    <button class="btn" onclick="testAuthentication()">测试认证状态</button>
                </div>
                <div>
                    <button class="btn" onclick="testSystemInfo()">测试系统信息</button>
                    <button class="btn" onclick="testDownloadRecords()">测试下载记录</button>
                </div>
            </div>
            <div id="testResults"></div>
        </div>

        <div class="test-item">
            <h4>📊 修复前后对比</h4>
            <div class="code">
修复前的错误：
❌ Access to fetch at 'http://localhost:8086/api/system/info' blocked by CORS
❌ Failed to load resource: net::ERR_FAILED
❌ TypeError: Failed to fetch
❌ 网络连接失败，请检查网络设置

修复后的预期结果：
✅ API连接正常
✅ 认证状态有效
✅ 系统信息加载成功（或优雅降级）
✅ 下载记录功能正常
            </div>
        </div>

        <div class="test-item">
            <h4>🚀 快速验证</h4>
            <button class="btn" onclick="openMainPage()">打开主页面</button>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn" onclick="checkConsoleErrors()">检查控制台错误</button>
        </div>

        <div class="test-item">
            <h4>📋 验证清单</h4>
            <div id="checklist">
                <div>□ 主页面加载无CORS错误</div>
                <div>□ 用户信息正确显示</div>
                <div>□ 系统信息加载成功或优雅降级</div>
                <div>□ 下载记录菜单可正常点击</div>
                <div>□ 下载记录界面正常显示</div>
                <div>□ 控制台无重复错误信息</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        function showResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function testAPIConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ API连接成功: ${result.status}`, 'success');
                } else {
                    showResult(`❌ API连接失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ API连接错误: ${error.message}`, 'error');
            }
        }

        async function testAuthentication() {
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    showResult('❌ 未找到认证信息', 'error');
                    return;
                }

                const auth = JSON.parse(authData);
                if (!auth.token) {
                    showResult('❌ 认证token缺失', 'error');
                    return;
                }

                const response = await fetch(`${API_BASE}/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${auth.token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`✅ 认证有效: 用户 ${result.user?.username}`, 'success');
                } else {
                    showResult(`❌ 认证失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 认证测试错误: ${error.message}`, 'error');
            }
        }

        async function testSystemInfo() {
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    showResult('⚠️ 需要先登录才能测试系统信息', 'warning');
                    return;
                }

                const auth = JSON.parse(authData);
                const response = await fetch(`${API_BASE}/system/info`, {
                    headers: {
                        'Authorization': `Bearer ${auth.token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`✅ 系统信息获取成功: 版本 ${result.data?.version}`, 'success');
                } else {
                    showResult(`⚠️ 系统信息获取失败: ${response.status} (这是正常的，会优雅降级)`, 'warning');
                }
            } catch (error) {
                showResult(`⚠️ 系统信息测试错误: ${error.message} (这是正常的，会优雅降级)`, 'warning');
            }
        }

        async function testDownloadRecords() {
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    showResult('⚠️ 需要先登录才能测试下载记录', 'warning');
                    return;
                }

                const auth = JSON.parse(authData);
                const response = await fetch(`${API_BASE}/download/records`, {
                    headers: {
                        'Authorization': `Bearer ${auth.token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const recordCount = result.records?.length || 0;
                    showResult(`✅ 下载记录获取成功: ${recordCount} 条记录`, 'success');
                } else {
                    showResult(`❌ 下载记录获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 下载记录测试错误: ${error.message}`, 'error');
            }
        }

        function openMainPage() {
            window.open('http://localhost:8084', '_blank');
        }

        async function runAllTests() {
            document.getElementById('testResults').innerHTML = '';
            showResult('🚀 开始运行所有测试...', 'info');
            
            await testAPIConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSystemInfo();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDownloadRecords();
            
            showResult('✅ 所有测试完成', 'success');
        }

        function checkConsoleErrors() {
            showResult('请打开浏览器开发者工具(F12)查看控制台，确认没有重复的错误信息', 'info');
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('错误修复测试页面已加载');
            console.log('修复内容：');
            console.log('1. 修复API端口配置 (8086 -> 8080)');
            console.log('2. 改进认证头处理逻辑');
            console.log('3. 增加系统信息API错误处理');
            console.log('4. 修复下载记录API响应处理');
        };
    </script>
</body>
</html>
