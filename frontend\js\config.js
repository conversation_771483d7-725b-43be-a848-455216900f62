/**
 * 前端配置文件
 * 包含API端点、常量和配置选项
 */

// 应用配置
const CONFIG = {
    // API配置
    API: {
        // 默认服务器地址
        DEFAULT_SERVER_URL: 'http://localhost:8080',
        
        // API端点 - 匹配后端API路径
        ENDPOINTS: {
            // 认证相关
            LOGIN: '/auth/login',
            LOGOUT: '/auth/logout',
            VERIFY_TOKEN: '/auth/verify',

            // 文件相关
            FILES: '/files',
            FOLDERS: '/files/folders',
            UPLOAD: '/files/upload',
            DOWNLOAD: '/files/{id}/download',
            THUMBNAIL: '/files/{id}/thumbnail',
            PREVIEW: '/files/{id}/preview',

            // 下载相关
            DOWNLOAD_SINGLE: '/download/single/{id}',
            DOWNLOAD_BATCH: '/download/batch',
            DOWNLOAD_FOLDER: '/download/folder/{id}',
            DOWNLOAD_FILE: '/download/file/{filename}',
            DOWNLOAD_PASSWORD: '/download/password/request',
            DOWNLOAD_RECORDS: '/download/records',
            PASSWORD_REQUESTS: '/download/password-requests',

            // 搜索相关
            SEARCH: '/search',

            // 收藏功能
            FAVORITES: '/favorites',
            FAVORITES_TOGGLE: '/favorites/toggle',
            FAVORITES_STATUS: '/favorites/status',
            FAVORITES_STATS: '/favorites/stats',

            // 系统相关
            SYSTEM_INFO: '/system/info',
            SYSTEM_STATUS: '/server/status',
            ONLINE_USERS: '/system/online-users',

            // 管理员相关
            ADMIN_USERS: '/admin/users',
            ADMIN_STATS: '/admin/stats',
            ADMIN_CONFIG: '/admin/config',

            // 配置相关
            PUBLIC_CONFIG: '/config/public',

            // 健康检查
            HEALTH: '/health'
        },
        
        // 请求超时时间（毫秒）
        TIMEOUT: 30000,
        
        // 重试次数
        RETRY_COUNT: 3,
        
        // 重试延迟（毫秒）
        RETRY_DELAY: 1000
    },
    
    // 文件配置 - 专门针对图片文件系统
    FILES: {
        // 支持的文件类型 - 只支持图片格式
        SUPPORTED_TYPES: {
            // 图片格式 - 根据需求只支持这些格式
            IMAGE: ['.jpg', '.jpeg', '.png', '.psd', '.tif', '.tiff', '.ai', '.eps', '.gif', '.bmp', '.webp', '.svg']
        },

        // 允许显示的文件扩展名（只有这些格式的文件会被显示）
        ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.psd', '.tif', '.tiff', '.ai', '.eps', '.gif', '.bmp', '.webp', '.svg'],
        
        // 文件图标映射
        ICONS: {
            // 文件夹
            folder: 'fas fa-folder',
            
            // 图片
            image: 'fas fa-image',
            
            // 视频
            video: 'fas fa-video',
            
            // 音频
            audio: 'fas fa-music',
            
            // 文档
            pdf: 'fas fa-file-pdf',
            word: 'fas fa-file-word',
            excel: 'fas fa-file-excel',
            powerpoint: 'fas fa-file-powerpoint',
            text: 'fas fa-file-alt',
            
            // 压缩包
            archive: 'fas fa-file-archive',
            
            // 代码
            code: 'fas fa-file-code',
            
            // 默认
            default: 'fas fa-file'
        },
        
        // 文件大小限制（字节）
        MAX_SIZE: 1024 * 1024 * 1024, // 1GB
        
        // 单次上传文件数量限制
        MAX_FILES: 50,
        
        // 缩略图尺寸
        THUMBNAIL_SIZE: {
            SMALL: 150,
            MEDIUM: 300,
            LARGE: 600
        }
    },
    
    // UI配置
    UI: {
        // 分页配置
        PAGINATION: {
            PAGE_SIZE: 20,
            MAX_PAGES: 10
        },
        
        // 动画配置
        ANIMATION: {
            DURATION: 300,
            EASING: 'ease-in-out'
        },
        
        // 通知配置
        NOTIFICATION: {
            DURATION: 5000,
            MAX_COUNT: 5
        },
        
        // 搜索配置
        SEARCH: {
            DEBOUNCE_DELAY: 300,
            MIN_QUERY_LENGTH: 1,  // 支持单字符搜索
            MAX_RESULTS: 100
        },
        
        // 上传配置
        UPLOAD: {
            CHUNK_SIZE: 1024 * 1024, // 1MB
            CONCURRENT_UPLOADS: 3,
            RETRY_COUNT: 3
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        USER_PREFERENCES: 'file_share_user_preferences',
        VIEW_MODE: 'file_share_view_mode',
        LAYOUT_MODE: 'file_share_layout_mode',
        SORT_ORDER: 'file_share_sort_order',
        RECENT_FOLDERS: 'file_share_recent_folders',
        FAVORITES: 'file_share_favorites',
        THEME: 'file_share_theme',
        TOKEN: 'file_share_token',
        USER_INFO: 'file_share_user_info'
    },
    
    // 默认用户偏好设置
    DEFAULT_PREFERENCES: {
        viewMode: 'grid', // grid | list
        sortBy: 'name', // name | date | size | type
        sortOrder: 'asc', // asc | desc
        showHiddenFiles: false,
        autoRefresh: true,
        refreshInterval: 30000, // 30秒
        theme: 'light', // light | dark | auto
        language: 'zh-CN'
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        NETWORK_ERROR: '网络连接失败，请检查网络设置',
        SERVER_ERROR: '服务器错误，请稍后重试',
        FILE_NOT_FOUND: '文件不存在或已被删除',
        PERMISSION_DENIED: '权限不足，无法执行此操作',
        FILE_TOO_LARGE: '文件大小超出限制',
        INVALID_FILE_TYPE: '不支持的文件类型',
        UPLOAD_FAILED: '文件上传失败',
        DOWNLOAD_FAILED: '文件下载失败',
        DELETE_FAILED: '文件删除失败',
        SEARCH_FAILED: '搜索失败，请重试',
        UNKNOWN_ERROR: '未知错误，请联系管理员'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        UPLOAD_SUCCESS: '文件上传成功',
        DOWNLOAD_SUCCESS: '文件下载成功',
        DELETE_SUCCESS: '文件删除成功',
        COPY_SUCCESS: '文件复制成功',
        MOVE_SUCCESS: '文件移动成功',
        RENAME_SUCCESS: '文件重命名成功',
        FOLDER_CREATED: '文件夹创建成功'
    },
    
    // 调试配置
    DEBUG: {
        ENABLED: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
        LOG_LEVEL: 'debug', // error | warn | info | debug - 临时启用debug日志
        SHOW_API_CALLS: true, // 临时启用API调用日志
        SHOW_PERFORMANCE: false // 关闭性能日志
    }
};

// 文件类型检测函数 - 只检测图片文件
CONFIG.FILES.getFileType = function(filename) {
    if (!filename) return 'unknown';

    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));

    // 只检查是否为支持的图片格式
    if (this.SUPPORTED_TYPES.IMAGE.includes(ext)) {
        return 'image';
    }

    return 'unknown';
};

// 检查文件是否为允许的格式
CONFIG.FILES.isAllowedFile = function(filename) {
    if (!filename) return false;

    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return this.ALLOWED_EXTENSIONS.includes(ext);
};

// 文件图标获取函数
CONFIG.FILES.getFileIcon = function(filename, isFolder = false) {
    if (isFolder) {
        return this.ICONS.folder;
    }
    
    if (!filename) return this.ICONS.default;
    
    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    const type = this.getFileType(filename);
    
    // 特殊文件类型图标
    const specialIcons = {
        '.pdf': this.ICONS.pdf,
        '.doc': this.ICONS.word,
        '.docx': this.ICONS.word,
        '.xls': this.ICONS.excel,
        '.xlsx': this.ICONS.excel,
        '.ppt': this.ICONS.powerpoint,
        '.pptx': this.ICONS.powerpoint,
        '.txt': this.ICONS.text,
        '.zip': this.ICONS.archive,
        '.rar': this.ICONS.archive,
        '.7z': this.ICONS.archive
    };
    
    if (specialIcons[ext]) {
        return specialIcons[ext];
    }
    
    // 按类型返回图标
    const typeIcons = {
        image: this.ICONS.image,
        video: this.ICONS.video,
        audio: this.ICONS.audio,
        document: this.ICONS.text,
        archive: this.ICONS.archive,
        code: this.ICONS.code
    };
    
    return typeIcons[type] || this.ICONS.default;
};

// 文件大小格式化函数
CONFIG.formatFileSize = function(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 日期格式化函数
CONFIG.formatDate = function(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const now = new Date();
    const diff = now - d;
    
    // 小于1分钟
    if (diff < 60000) {
        return '刚刚';
    }
    
    // 小于1小时
    if (diff < 3600000) {
        return Math.floor(diff / 60000) + ' 分钟前';
    }
    
    // 小于1天
    if (diff < 86400000) {
        return Math.floor(diff / 3600000) + ' 小时前';
    }
    
    // 小于7天
    if (diff < 604800000) {
        return Math.floor(diff / 86400000) + ' 天前';
    }
    
    // 超过7天显示具体日期
    return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 调试日志函数
CONFIG.log = function(level, message, ...args) {
    if (!this.DEBUG.ENABLED) return;
    
    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.DEBUG.LOG_LEVEL);
    const messageLevelIndex = levels.indexOf(level);
    
    if (messageLevelIndex <= currentLevelIndex) {
        const timestamp = new Date().toISOString();
        console[level](`[${timestamp}] ${message}`, ...args);
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局可用
window.CONFIG = CONFIG;
