#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime, timedelta

def create_test_records():
    """直接通过SQL创建测试下载记录"""
    print("📝 通过SQL创建测试下载记录...")

    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user = cursor.fetchone()

            if not user:
                print("❌ 找不到用户fjj")
                return False

            user_id = user[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")

            # 清除现有的测试记录
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id = %s", (user_id,))
            existing_count = cursor.fetchone()[0]
            print(f"📊 现有下载记录数量: {existing_count}")

            if existing_count > 0:
                cursor.execute("DELETE FROM download_records WHERE user_id = %s", (user_id,))
                print("🗑️ 清除了现有的测试记录")

            # 创建测试记录
            test_records = []
            for i in range(5):
                download_time = datetime.now() - timedelta(days=i, hours=i*2)
                is_encrypted = i % 2 == 0

                cursor.execute("""
                    INSERT INTO download_records
                    (file_id, user_id, download_type, zip_filename, zip_path, file_size,
                     is_encrypted, password, download_status, created_at, downloaded_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    i + 1,  # file_id
                    user_id,
                    'single',
                    f"test_file_{i+1}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    f"/tmp/downloads/test_file_{i+1}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    1024 * 1024 * (i + 1),  # 不同大小
                    is_encrypted,
                    f"pass{i+1}" if is_encrypted else None,
                    'completed',
                    download_time,
                    download_time
                ))
                test_records.append(i+1)

            # 提交事务
            connection.commit()
            print(f"✅ 创建了 {len(test_records)} 条测试下载记录")

            # 查询并显示创建的记录
            cursor.execute("""
                SELECT id, file_id, zip_filename, download_type, is_encrypted,
                       file_size, created_at, downloaded_at
                FROM download_records
                WHERE user_id = %s
                ORDER BY created_at DESC
            """, (user_id,))

            records = cursor.fetchall()
            print(f"\n📋 创建的下载记录:")
            print("ID | 文件ID | 压缩文件名 | 类型 | 加密 | 大小(MB) | 创建时间")
            print("-" * 80)

            for record in records:
                file_size_mb = record[5] / (1024 * 1024) if record[5] else 0
                print(f"{record[0]:3d} | {record[1]:6d} | {record[2][:25]:25s} | {record[3]:6s} | {record[4]:5s} | {file_size_mb:8.1f} | {record[6]}")

        connection.close()
        return True

    except Exception as e:
        print(f"❌ 创建测试记录失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_test_records()
